import React from 'react';
import { Star } from 'lucide-react';
import { TestimonialType } from '@/lib/types';
import { cn } from '@/lib/utils';

interface AverageRatingProps {
  testimonials: TestimonialType[];
  className?: string;
  showCount?: boolean;
}

const AverageRating: React.FC<AverageRatingProps> = ({
  testimonials,
  className = '',
  showCount = true
}) => {
  if (!testimonials.length) return null;

  // Calculate average rating
  const totalRating = testimonials.reduce(
    (sum, testimonial) => sum + testimonial.rating,
    0
  );
  const averageRating = totalRating / testimonials.length;
  const roundedRating = Math.round(averageRating * 10) / 10; // Round to 1 decimal place

  return (
    <div className={cn('flex items-center', className)}>
      <div className="flex items-center mr-2">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={cn(
              'w-5 h-5',
              i < Math.floor(roundedRating)
                ? 'text-yellow-400 fill-current'
                : i < roundedRating
                ? 'text-yellow-400 fill-current opacity-50'
                : 'text-gray-300'
            )}
          />
        ))}
      </div>
      <span className="font-medium">{roundedRating.toFixed(1)}</span>
      {showCount && (
        <span className="text-gray-500 text-sm ml-2">
          ({testimonials.length}{' '}
          {testimonials.length === 1 ? 'vurdering' : 'vurderinger'})
        </span>
      )}
    </div>
  );
};

export default AverageRating;