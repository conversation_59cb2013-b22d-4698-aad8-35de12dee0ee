import React from 'react';
import { cn  } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
  animate?: boolean;
}

const Skeleton: React.FC<SkeletonProps> = ({ 
  className,
  animate = true
}) => {
  return (
    <div
      className={cn(
        'bg-gray-200 rounded-md',
        animate && 'animate-pulse',
        className
      )}
    />
  );
};

export { Skeleton };
export default Skeleton;