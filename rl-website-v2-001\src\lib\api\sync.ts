/**
 * Synchronous versions of API functions
 *
 * This module provides synchronous versions of the API functions
 * for direct use in components that don't want to use the async API.
 *
 * These functions are meant to be used as a transitional solution
 * while migrating components to use the async API with the useData hook.
 *
 * @deprecated This module is being phased out in favor of the async API.
 * Please use the async API with the useData hook instead.
 */

// Import from constants to avoid direct data imports
import { SERVICES, PROJECTS, TESTIMONIALS, SERVICE_AREAS } from '@/lib/constants/data';
import { ProjectType, ServiceType, TestimonialType, ServiceArea } from '@/lib/types';
import { logDataAccess } from '@/lib/utils/debug';

/**
 * Get all services (synchronous version)
 */
export const getServices = (): ServiceType[] => {
  logDataAccess('sync', 'services');
  return SERVICES;
};

/**
 * Get a service by ID (synchronous version)
 */
export const getServiceById = (id: string): ServiceType | undefined => {
  logDataAccess('sync', 'service', { id });
  return SERVICES.find(service => service.id === id);
};

/**
 * Get service image category (synchronous version)
 */
export const getServiceImageCategory = (serviceId: string): string | undefined => {
  logDataAccess('sync', 'serviceImageCategory', { serviceId });
  const service = SERVICES.find(service => service.id === serviceId);
  return service?.imageCategory;
};

/**
 * Get all projects (synchronous version)
 */
export const getProjects = (): ProjectType[] => {
  logDataAccess('sync', 'projects');
  return PROJECTS;
};

/**
 * Get a project by ID (synchronous version)
 */
export const getProjectById = (id: string): ProjectType | undefined => {
  logDataAccess('sync', 'project', { id });
  return PROJECTS.find(project => project.id === id);
};

/**
 * Get all testimonials (synchronous version)
 */
export const getTestimonials = (): TestimonialType[] => {
  logDataAccess('sync', 'testimonials');
  return TESTIMONIALS;
};

/**
 * Get service areas (synchronous version)
 */
export const getServiceAreas = (): ServiceArea[] => {
  logDataAccess('sync', 'serviceAreas');
  return SERVICE_AREAS;
};
