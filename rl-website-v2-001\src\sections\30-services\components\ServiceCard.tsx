import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { ServiceType } from '@/lib/types';
import { cn } from '@/lib/utils';

interface ServiceCardProps {
  service: ServiceType;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
  service,
  className = '',
  variant = 'default'
}) => {
  const { id, title, description, image, features } = service;

  if (variant === 'compact') {
    return (
      <div className={cn(
        'flex flex-col sm:flex-row gap-4 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow',
        className
      )}>
        <div className="w-full sm:w-1/3 h-40 sm:h-auto rounded-md overflow-hidden">
          <img
            src={image}
            alt={title}
            className="w-full h-full object-cover"
            loading="lazy"
          />
        </div>
        <div className="flex-1">
          <h3 className="text-xl font-semibold mb-2">{title}</h3>
          <p className="text-gray-600 mb-4 line-clamp-3">
            {description}
          </p>
          <Link
            to={`/tjenester/${id}`}
            className="text-green-600 font-medium hover:text-green-700 transition-colors flex items-center gap-1"
          >
            Les mer
            <ArrowRight className="w-4 h-4" />
          </Link>
        </div>
      </div>
    );
  }

  if (variant === 'featured') {
    return (
      <div className={cn(
        'bg-white rounded-lg shadow-md overflow-hidden',
        className
      )}>
        <div className="h-48 overflow-hidden">
          <img
            src={image}
            alt={title}
            className="w-full h-full object-cover"
            loading="lazy"
          />
        </div>
        <div className="p-6">
          <h3 className="text-xl font-semibold mb-3">{title}</h3>
          <p className="text-gray-600 mb-4 line-clamp-3">
            {description}
          </p>
          {features && features.length > 0 && (
            <ul className="mb-4 space-y-1">
              {features.slice(0, 3).map((feature, index) => (
                <li key={index} className="flex items-start text-sm text-gray-600">
                  <span className="text-green-500 mr-2">•</span>
                  {feature}
                </li>
              ))}
            </ul>
          )}
          <Link
            to={`/tjenester/${id}`}
            className="inline-block bg-green-500 text-white px-5 py-2 rounded-md hover:bg-green-600 transition-colors"
          >
            Les mer
          </Link>
        </div>
      </div>
    );
  }

  // Default variant
  return (
    <div className={cn(
      'relative h-[400px] group overflow-hidden rounded-lg',
      className
    )}>
      <div
        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
        style={{ backgroundImage: `url(${image})` }}
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent" />
      <div className="absolute bottom-0 left-0 right-0 p-6 text-white transform transition-transform duration-300">
        <h3 className="text-2xl font-semibold mb-3">{title}</h3>
        <p className="text-gray-200 mb-4 line-clamp-3">{description}</p>
        <Link
          to={`/tjenester/${id}`}
          className="inline-flex items-center gap-2 bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 transition-colors"
        >
          Les mer
          <ArrowRight className="w-4 h-4" />
        </Link>
      </div>
    </div>
  );
};

export default ServiceCard;