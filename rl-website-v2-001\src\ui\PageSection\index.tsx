import React from 'react';
import { cn  } from '@/lib/utils';
import Container from '@/ui/Container';

type SectionWidth = 'full' | 'wide' | 'medium' | 'narrow';
type SectionBackground = 'white' | 'light' | 'dark' | 'primary';
type SectionSpacing = 'none' | 'small' | 'medium' | 'large';

interface PageSectionProps {
  children: React.ReactNode;
  className?: string;
  width?: SectionWidth;
  background?: SectionBackground;
  spacing?: SectionSpacing;
  centered?: boolean;
  as?: React.ElementType;
  id?: string;
}

/**
 * PageSection component for consistent layout sections across the site
 *
 * This component provides standardized spacing, width constraints, and background
 * options to ensure consistent layout patterns throughout the site.
 */
const PageSection: React.FC<PageSectionProps> = ({
  children,
  className,
  width = 'full',
  background = 'white',
  spacing = 'medium',
  centered = false,
  as: Component = 'section',
  id
}) => {
  // Width constraints
  const widthClasses = {
    full: '',
    wide: 'max-w-7xl mx-auto',
    medium: 'max-w-4xl mx-auto',
    narrow: 'max-w-2xl mx-auto'
  };

  // Background colors
  const backgroundClasses = {
    white: 'bg-white',
    light: 'bg-gray-50',
    dark: 'bg-gray-900 text-white',
    primary: 'bg-green-50'
  };

  // Spacing (padding)
  const spacingClasses = {
    none: 'py-0',
    small: 'py-4 sm:py-6',
    medium: 'py-8 sm:py-12',
    large: 'py-12 sm:py-16 md:py-20'
  };

  // Text alignment
  const alignmentClasses = centered ? 'text-center' : '';

  // Outer section element with background and padding
  return (
    <Component
      className={cn(backgroundClasses[background], className)}
      id={id}
    >
      <Container className={spacingClasses[spacing]}>
        <div className={cn(widthClasses[width], alignmentClasses)}>
          {children}
        </div>
      </Container>
    </Component>
  );
};

export { PageSection };
export default PageSection;