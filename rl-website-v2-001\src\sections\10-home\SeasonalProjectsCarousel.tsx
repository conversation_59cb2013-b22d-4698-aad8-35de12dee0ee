import React, { useState, useEffect, useCallback } from 'react';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useData, useSeasonalData } from '@/lib/hooks';
import { getSeasonalProjects } from '@/lib/api';
import ProjectCard from '@/sections/40-projects/ProjectCard';
import { logSeasonalAccess } from '@/lib/utils/debug';

const SeasonalProjectsCarousel = () => {
  // Define projectsPerPage as a constant to use throughout the component
  const projectsPerPage = 1;

  const navigate = useNavigate();
  // Get current season data
  const { currentSeason } = useSeasonalData();

  // Log seasonal access for debugging
  useEffect(() => {
    logSeasonalAccess('SeasonalProjectsCarousel', currentSeason, {
      component: 'SeasonalProjectsCarousel'
    });
  }, [currentSeason]);

  const { data: seasonalProjects, loading } = useData(() => getSeasonalProjects(9), []);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [touchStart, setTouchStart] = useState<number | null>(null);

  const handleProjectClick = useCallback((id: string) => {
    navigate(`/prosjekter/${id}`);
  }, [navigate]);

  const handlePrevious = useCallback(() => {
    if (isAnimating || !seasonalProjects?.length) return;
    setIsAnimating(true);

    // For desktop view, we need to calculate based on pages
    const totalPages = Math.ceil(seasonalProjects.length / projectsPerPage);

    setCurrentIndex((prev) => (prev === 0 ? totalPages - 1 : prev - 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, seasonalProjects?.length, projectsPerPage]);

  const handleNext = useCallback(() => {
    if (isAnimating || !seasonalProjects?.length) return;
    setIsAnimating(true);

    // For desktop view, we need to calculate based on pages
    const totalPages = Math.ceil(seasonalProjects.length / projectsPerPage);

    setCurrentIndex((prev) => (prev === totalPages - 1 ? 0 : prev + 1));
    setTimeout(() => setIsAnimating(false), 500);
  }, [isAnimating, seasonalProjects?.length, projectsPerPage]);

  useEffect(() => {
    const interval = setInterval(handleNext, 5000);
    return () => clearInterval(interval);
  }, [handleNext]);

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.touches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!touchStart) return;

    const touchEnd = e.touches[0].clientX;
    const diff = touchStart - touchEnd;

    if (Math.abs(diff) > 50) {
      if (diff > 0) {
        handleNext();
      } else {
        handlePrevious();
      }
      setTouchStart(null);
    }
  };

  if (loading) {
    return (
      <div className="aspect-square bg-gray-200 animate-pulse rounded-lg"></div>
    );
  }

  if (!seasonalProjects?.length) {
    return null;
  }

  // Calculate total pages based on projectsPerPage
  const totalPages = Math.ceil(seasonalProjects.length / projectsPerPage);

  // Get current page projects
  const getCurrentPageProjects = () => {
    const startIdx = currentIndex * projectsPerPage;
    const endIdx = Math.min(startIdx + projectsPerPage, seasonalProjects.length);
    return seasonalProjects.slice(startIdx, endIdx);
  };

  return (
    <section className="my-8">
      <div className="relative group">
        {/* Desktop View - Single Project */}
        <div className="hidden sm:block">
          <div className="w-full max-w-3xl mx-auto">
            <div className="aspect-[4/3] w-full">
              {getCurrentPageProjects().map((project) => (
                <div key={project.id} className="rounded-lg overflow-hidden h-full">
                  <ProjectCard
                    project={project}
                    onProjectClick={handleProjectClick}
                    showTestimonial={true}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Mobile View - Single Carousel */}
        <div
          className="sm:hidden overflow-hidden rounded-lg"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
        >
          <div
            className="relative transition-transform duration-500 ease-in-out"
            style={{
              transform: `translateX(-${currentIndex * 100}%)`,
              display: 'flex'
            }}
          >
            {seasonalProjects.map((project) => (
              <div
                key={project.id}
                className="w-full flex-shrink-0"
              >
                <div className="aspect-[4/3] w-full">
                  <ProjectCard
                    project={project}
                    onProjectClick={handleProjectClick}
                    showTestimonial={true}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Navigation Buttons */}
        {totalPages > 1 && (
          <>
            <button
              onClick={handlePrevious}
              className="absolute left-2 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 shadow-lg text-gray-700 hover:bg-white hover:text-gray-900 transition-all duration-300 opacity-70 hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 z-10"
              aria-label="Forrige prosjekter"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>

            <button
              onClick={handleNext}
              className="absolute right-2 top-1/2 -translate-y-1/2 p-3 rounded-full bg-white/90 shadow-lg text-gray-700 hover:bg-white hover:text-gray-900 transition-all duration-300 opacity-70 hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 z-10"
              aria-label="Neste prosjekter"
            >
              <ArrowRight className="w-5 h-5" />
            </button>
          </>
        )}

        {/* Pagination Indicators */}
        {totalPages > 1 && (
          <div className="mt-4 flex justify-center gap-2">
            {Array.from({ length: totalPages }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-green-500 w-4'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
                aria-label={`Gå til side ${index + 1}`}
                aria-current={index === currentIndex}
              />
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default SeasonalProjectsCarousel;