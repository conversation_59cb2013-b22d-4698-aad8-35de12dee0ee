import React from 'react';
import { MapPin, Calendar, Tag } from 'lucide-react';
import { ProjectType } from '@/lib/types';
import { cn } from '@/lib/utils';
import { encodeImagePath } from '@/lib/utils/paths';

interface ProjectCardProps {
  project: ProjectType;
  variant?: 'default' | 'featured' | 'compact';
  className?: string;
  showTestimonial?: boolean;
  onProjectClick?: (id: string) => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  // variant is not used but kept for API compatibility
  variant: _ = 'default',
  className = '',
  showTestimonial = false,
  onProjectClick
}) => {
  const handleClick = () => {
    if (onProjectClick) {
      onProjectClick(project.id);
    }
  };

  return (
    <button
      onClick={handleClick}
      className={cn(
        "relative w-full h-full text-left",
        "transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2",
        className
      )}
    >
      <div
        className="absolute inset-0 bg-cover bg-center transition-transform duration-500 hover:scale-105"
        style={{
          backgroundImage: `url(${encodeImagePath(project.image)})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
        aria-hidden="true"
      />
      <div
        className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent"
        aria-hidden="true"
      />

      <div className="absolute bottom-0 left-0 right-0 p-6">
        <div className="flex flex-wrap items-center gap-2 mb-2">
          <span className="inline-flex items-center gap-1.5 bg-green-500 text-white px-2.5 py-1 rounded-full text-xs sm:text-sm font-medium">
            {project.category}
          </span>
          <div className="flex items-center gap-1.5 text-white/90">
            <MapPin className="w-4 h-4" />
            <span className="text-xs sm:text-sm">{project.location}</span>
          </div>
          <div className="flex items-center gap-1.5 text-white/90">
            <Calendar className="w-4 h-4" />
            <span className="text-xs sm:text-sm">{project.completionDate}</span>
          </div>
        </div>

        <h3 className="text-lg sm:text-xl font-medium mb-2 text-white line-clamp-1">
          {project.title}
        </h3>
        <p className="text-gray-200 mb-3 text-sm leading-relaxed line-clamp-2">
          {project.description}
        </p>

        {/* Project tags */}
        {project.tags && project.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-2">
            {project.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center gap-1 px-2 py-0.5 bg-white/10 rounded-full text-xs text-white/80"
              >
                <Tag className="w-3 h-3" />
                {tag}
              </span>
            ))}
          </div>
        )}

        {/* Testimonial */}
        {showTestimonial && project.testimonial && (
          <div className="mt-2 p-2 bg-black/30 rounded text-white/90 text-sm italic">
            "{project.testimonial.quote}" — {project.testimonial.author}
          </div>
        )}
      </div>

      {/* Hidden text for screen readers */}
      <span className="sr-only">
        Se detaljer om prosjektet {project.title}
      </span>
    </button>
  );
};

export default ProjectCard;

export { ProjectCard }