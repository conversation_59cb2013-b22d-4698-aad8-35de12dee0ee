{"name": "ringerike-landskap-website", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:staging": "tsc && vite build --mode staging", "build:production": "tsc && vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "preview:staging": "vite preview --mode staging", "preview:production": "vite preview --mode production", "validate-env": "node scripts/validate-env-files.js"}, "dependencies": {"clsx": "^2.1.0", "framer-motion": "^12.5.0", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-router-dom": "^6.22.3", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@types/node": "^20.11.24", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-helmet": "^6.1.11", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "cross-env": "^7.0.3", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}