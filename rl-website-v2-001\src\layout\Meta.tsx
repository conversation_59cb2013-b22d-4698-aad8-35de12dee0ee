import React from "react";
import { Helmet } from "react-helmet-async";
import { SITE_CONFIG } from "@/lib/constants/site";
import { getCompanySchema } from "@/lib/constants/contact";
import { generateSEOMeta } from "@/lib/utils/seo";

interface MetaProps {
    title?: string;
    description?: string;
    image?: string;
    type?: "website" | "article";
    noindex?: boolean;
    schema?: Record<string, unknown>;
    keywords?: string[];
    location?: {
        city: string;
        region?: string;
    };
}

export const Meta: React.FC<MetaProps> = ({
    title,
    description = SITE_CONFIG.description,
    image = SITE_CONFIG.ogImage,
    type = "website",
    noindex = false,
    schema,
    keywords = SITE_CONFIG.keywords,
    location,
}) => {
    const fullTitle = title
        ? `${title} | ${SITE_CONFIG.name}`
        : location
        ? `Anleggsgartner i ${location.city} | ${SITE_CONFIG.name}`
        : SITE_CONFIG.name;

    const localDescription = location
        ? `Din lokale anleggsgartner i ${location.city}. Vi har inngående kjennskap til lokale forhold og leverer skreddersydde løsninger for ditt uterom.`
        : description;

    // Generate SEO meta tags using our utility
    const seoMeta = generateSEOMeta({
        title: fullTitle,
        description: localDescription,
        image,
        type,
        path: typeof window !== "undefined" ? window.location.pathname : "",
    });

    // Get the base schema from our contact utility
    const baseSchema = {
        ...getCompanySchema(),
        description: localDescription,
        image: SITE_CONFIG.ogImage,
        "@id": SITE_CONFIG.url,
        url: SITE_CONFIG.url,
        hasOfferCatalog: {
            "@type": "OfferCatalog",
            name: "Anleggsgartnertjenester i Hole og Ringerike",
            itemListElement: [
                {
                    "@type": "Offer",
                    itemOffered: {
                        "@type": "Service",
                        name: "Belegningsstein",
                        description:
                            "Profesjonell legging av belegningsstein tilpasset lokale forhold",
                    },
                },
            ],
        },
    };

    return (
        <Helmet>
            <title>{seoMeta.title}</title>
            {/* Apply all meta tags from our SEO utility */}
            {seoMeta.meta.map((meta, index) => (
                <meta key={`meta-${index}`} {...meta} />
            ))}
            {/* Apply all link tags from our SEO utility */}
            {seoMeta.link.map((link, index) => (
                <link key={`link-${index}`} {...link} />
            ))}
            {/* Keep existing functionality */}
            {keywords && keywords.length > 0 && (
                <meta name="keywords" content={keywords.join(", ")} />
            )}
            {noindex && <meta name="robots" content="noindex" />}
            {schema && (
                <script type="application/ld+json">
                    {JSON.stringify(schema)}
                </script>
            )}
            {!schema && (
                <script type="application/ld+json">
                    {JSON.stringify(baseSchema)}
                </script>
            )}
        </Helmet>
    );
};
