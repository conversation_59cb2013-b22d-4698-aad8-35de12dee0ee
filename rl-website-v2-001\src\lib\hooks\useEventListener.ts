import { useEffect, useRef } from 'react';

type EventHandler<T> = (event: T) => void;

export const useEventListener = <K extends keyof WindowEventMap>(
  eventName: K,
  handler: EventHandler<WindowEventMap[K]>,
  element: Window | Element | null = window,
  options?: boolean | AddEventListenerOptions
) => {
  const savedHandler = useRef<EventHandler<WindowEventMap[K]>>();

  useEffect(() => {
    savedHandler.current = handler;
  }, [handler]);

  useEffect(() => {
    if (!element || !savedHandler.current) return;

    // Use type assertion to fix TypeScript error
    const eventListener = ((event: Event) => savedHandler.current!(event as WindowEventMap[K])) as EventListener;
    element.addEventListener(eventName, eventListener, options);

    return () => {
      element.removeEventListener(eventName, eventListener, options);
    };
  }, [eventName, element, options]);
};