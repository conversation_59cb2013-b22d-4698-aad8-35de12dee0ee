
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useData } from "@/lib/hooks";
import {
    getProjectById,
    getFilteredProjects,
    getServiceById
} from "@/lib/api";
import {
    <PERSON>,
    <PERSON>ton,
    Card,
    PageSection,
    SectionHeading,
    ContentGrid,
    Loading
} from "@/ui";
import { MapPin, Calendar } from "lucide-react";
import { ProjectType } from "@/lib/types";

const ProjectDetail = () => {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();

    // Use the useData hook to fetch the project
    const { data: project, loading: projectLoading, error: projectError } =
        useData(() => getProjectById(id || ""), [id]);

    // Use the useData hook to fetch the service - always call this hook
    const { data: service } =
        useData(() => {
            if (!project) return Promise.resolve(null);
            return getServiceById(project.category);
        }, [project?.category]);

    // Use the useData hook to fetch similar projects - always call this hook
    const { data: similarProjects = [] } =
        useData(() => {
            if (!project) return Promise.resolve([]);

            // First try to find projects with matching tags
            return getFilteredProjects(undefined, undefined, project.tags[0])
                .then(projects => {
                    // Filter out the current project and limit to 2
                    const filtered = projects.filter(p => p.id !== project.id).slice(0, 2);

                    // If we found matching projects, return them
                    if (filtered.length > 0) return filtered;

                    // Otherwise, try to find projects from the same location
                    return getFilteredProjects(undefined, project.location)
                        .then(locationProjects => {
                            const locationFiltered = locationProjects
                                .filter(p => p.id !== project.id)
                                .slice(0, 2);

                            return locationFiltered;
                        });
                });
        }, [project?.id, project?.tags, project?.location]);

    // Handle error and loading states after all hooks have been called
    if (projectError || (!projectLoading && !project)) {
        navigate("/");
        return null;
    }

    // Show loading state while data is being fetched
    if (projectLoading) {
        return <Loading message="Laster prosjektinformasjon..." />;
    }

    if (!project) {
        return <Loading message="Laster prosjektinformasjon..." />;
    }

    return (
        <div className="flex flex-col min-h-screen">
            <Hero title={project.title} backgroundImage={project.image} />

            {/* Project details section */}
            <PageSection width="medium" spacing="medium">
                <div className="flex flex-wrap items-center gap-3 mb-6">
                    <Button
                        to={`/tjenester/${service?.id || ''}`}
                        variant="primary"
                        className="!px-4 !py-2"
                    >
                        {service?.title || project.category}
                    </Button>
                    <div className="flex items-center gap-2 text-gray-600">
                        <MapPin className="w-4 h-4" />
                        <span>{project.location}</span>
                    </div>
                    <div className="flex items-center gap-2 text-gray-600">
                        <Calendar className="w-4 h-4" />
                        <span>{project.completionDate}</span>
                    </div>
                </div>

                <div className="prose prose-lg max-w-none">
                    <p className="text-gray-600 text-lg leading-relaxed">
                        {project.description}
                    </p>
                </div>
            </PageSection>

            {/* Similar services section */}
            {service && (
                <PageSection width="medium" background="light" spacing="medium">
                    <SectionHeading
                        title="Lignende tjenester"
                        size="medium"
                    />
                    <div className="bg-white rounded-lg shadow-sm p-6">
                        <h3 className="text-xl font-semibold mb-3">
                            {service.title}
                        </h3>
                        <p className="text-gray-600 mb-6">
                            {service.description}
                        </p>
                        <Button
                            to={`/tjenester/${service.id}`}
                            variant="primary"
                        >
                            Les mer om {service.title.toLowerCase()}
                        </Button>
                    </div>
                </PageSection>
            )}

            {/* Similar projects section */}
            {similarProjects && similarProjects.length > 0 && (
                <PageSection width="wide" spacing="medium">
                    <SectionHeading
                        title="Lignende prosjekter"
                        align="center"
                        size="medium"
                    />
                    <ContentGrid
                        columns={1}
                        mdColumns={2}
                        lgColumns={3}
                        gap="medium"
                    >
                        {similarProjects.map((similarProject: ProjectType) => (
                            <Card
                                key={similarProject.id}
                                title={similarProject.title}
                                description={similarProject.description}
                                image={similarProject.image}
                                link={`/prosjekter/${similarProject.id}`}
                                variant="hover"
                            />
                        ))}
                    </ContentGrid>
                </PageSection>
            )}
        </div>
    );
};

export default ProjectDetail;
