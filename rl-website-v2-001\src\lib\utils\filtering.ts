/**
 * Consolidated filtering utilities for services and projects
 *
 * This module provides a centralized place for all filtering functions used throughout the application.
 * It combines both general filtering and seasonal filtering to create a single source of truth.
 */

// Import types and utilities using absolute paths
import { ServiceType, ProjectType, SeasonType } from '@/lib/types/content';
import { normalizeString, normalizedStringCompare } from './strings';
import {
  MONTH_TO_SEASON,
  SEASON_DISPLAY_NAMES,
  SEASON_MAPPING,
  SEASONAL_SERVICES,
  SEASONAL_PROJECTS
} from '@/lib/constants/seasonal';
import {
  getServiceCategoriesForSeason,
  getServiceFeaturesForSeason,
  getServiceIdForCategory,
  SERVICE_CATEGORIES
} from '@/lib/constants/service-mappings';

/**
 * Gets the current season based on the current month
 * @returns The current season ('vinter', 'vår', 'sommer', or 'høst')
 */
export const getCurrentSeason = (): SeasonType => {
  const month = new Date().getMonth();
  return MONTH_TO_SEASON[month];
};

/**
 * Gets the display name for a season
 * @param season The season to get the display name for
 * @returns The display name for the season (e.g., 'Våren' for 'vår')
 */
export const getSeasonDisplayName = (season: SeasonType): string => {
  return SEASON_DISPLAY_NAMES[season] || season;
};

/**
 * Converts a Norwegian season name to English
 * @param season The Norwegian season name
 * @returns The English season name
 */
export const getEnglishSeasonName = (season: SeasonType): string => {
  return SEASON_MAPPING[season] || season;
};

/**
 * Extract the main category from a service title
 * @param service The service object
 * @returns The full category name from service-mappings.ts
 */
export const getServiceMainCategory = (service: ServiceType): string => {
  // First try to find a category that maps to this service
  const matchingCategory = Object.values(SERVICE_CATEGORIES).find(
    category => category.serviceId === service.id
  );

  // If we found a matching category, use its name
  if (matchingCategory) {
    return matchingCategory.name;
  }

  // Fall back to the old method if no mapping is found
  const words = service.title.split(' ');
  return words.length > 1 ? words[0] : service.title;
};

/**
 * Checks if a service matches a season
 * @param service The service to check
 * @param season The season to check against
 * @param strictMode Whether to use strict matching (primarily for winter season)
 * @returns Whether the service matches the season
 */
export const serviceMatchesSeason = (
  service: ServiceType,
  season: SeasonType,
  strictMode = false
): boolean => {
  // Get the service categories and features for this season from our consolidated mapping
  const seasonCategories = getServiceCategoriesForSeason(season);
  const seasonFeatures = getServiceFeaturesForSeason(season);

  if (!seasonCategories.length && !seasonFeatures.length) return false;

  // Extract the main category from the service title
  const serviceMainCategory = getServiceMainCategory(service);

  // Check for exact category match
  const exactCategoryMatch = seasonCategories.some(category =>
    normalizeString(serviceMainCategory) === normalizeString(category) ||
    normalizeString(service.title).startsWith(normalizeString(category))
  );

  // If we're in strict mode (for winter) or we have an exact match, return the result
  if (strictMode || exactCategoryMatch) {
    return exactCategoryMatch;
  }

  // For non-strict mode and no exact match, check for feature matches
  return service.features.some(feature =>
    seasonFeatures.some(seasonFeature =>
      normalizedStringCompare(feature, seasonFeature)
    )
  );
};

/**
 * Checks if a project matches a season
 * @param project The project to check
 * @param season The season to check against
 * @returns Whether the project matches the season
 */
export const projectMatchesSeason = (project: ProjectType, season: SeasonType): boolean => {
  // For backward compatibility, we'll still use the SEASONAL_PROJECTS mapping
  // In a future update, this could be migrated to the new mapping system
  const seasonMapping = SEASONAL_PROJECTS[season];
  if (!seasonMapping) return false;

  // Check if project category matches any of the seasonal categories
  const categoryMatch = seasonMapping.categories.some(category =>
    normalizedStringCompare(project.category, category)
  );

  if (categoryMatch) return true;

  // Check if any project tags match seasonal tags
  return project.tags.some(tag =>
    seasonMapping.tags.some(seasonTag =>
      normalizedStringCompare(tag, seasonTag)
    )
  );
};

/**
 * Check if a service matches a category
 * @param service The service to check
 * @param category The category to check against
 * @returns Whether the service matches the category
 */
export const serviceMatchesCategory = (service: ServiceType, category: string): boolean => {
  // Get the service ID for the category using our inherent relationship
  const categoryServiceId = getServiceIdForCategory(category);

  // If we have a direct service ID match, use that (most reliable)
  if (categoryServiceId && service.id === categoryServiceId) {
    return true;
  }

  // Fall back to string matching for backward compatibility
  const serviceMainCategory = getServiceMainCategory(service);
  return normalizedStringCompare(serviceMainCategory, category) ||
         normalizeString(service.title).startsWith(normalizeString(category));
};

/**
 * Check if a service has a specific feature
 * @param service The service to check
 * @param feature The feature to check for
 * @returns Whether the service has the feature
 */
export const serviceHasFeature = (service: ServiceType, feature: string): boolean => {
  return service.features.some(f => normalizedStringCompare(f, feature));
};

/**
 * Filter services based on criteria
 * @param services The services to filter
 * @param category Optional category filter (child of both season and feature)
 * @param feature Optional feature filter (child of season, parent of category)
 * @param season Optional season filter (parent of both feature and category)
 * @returns Filtered services
 */
export const filterServices = (
  services: ServiceType[],
  category?: string | null,
  feature?: string | null,
  season?: string | null
): ServiceType[] => {
  // If no filters are applied, return all services
  if (!season && !category && !feature) {
    return [...services];
  }

  // Apply filters in a hierarchical parent/child relationship
  let filtered = [...services];

  // Step 1: Filter by season first (top-level parent filter)
  if (season) {
    // Use strict mode for winter season
    const strictMode = season === 'vinter';
    filtered = filtered.filter(service =>
      serviceMatchesSeason(service, season as SeasonType, strictMode)
    );
  }

  // Step 2: Filter by feature (mid-level filter - child of season, parent of category)
  if (feature && filtered.length > 0) {
    filtered = filtered.filter(service =>
      serviceHasFeature(service, feature)
    );
  }

  // Step 3: Filter by category (lowest-level filter - child of both season and feature)
  if (category) {
    filtered = filtered.filter(service =>
      serviceMatchesCategory(service, category)
    );
  }

  // If no services match the filters, return all services
  if (filtered.length === 0) {
    return [...services];
  }

  return filtered;
};

/**
 * Check if a project matches a category
 * @param project The project to check
 * @param category The category to check against
 * @returns Whether the project matches the category
 */
export const projectMatchesCategory = (project: ProjectType, category: string): boolean => {
  const normalizedProjectCategory = normalizeString(project.category);
  const normalizedCategory = normalizeString(category);

  // Check if one contains the other (handles singular/plural variations)
  return normalizedProjectCategory.includes(normalizedCategory) ||
         normalizedCategory.includes(normalizedProjectCategory);
};

/**
 * Check if a project matches a location
 * @param project The project to check
 * @param location The location to check against
 * @returns Whether the project matches the location
 */
export const projectMatchesLocation = (project: ProjectType, location: string): boolean => {
  return normalizedStringCompare(project.location, location);
};

/**
 * Check if a project has a specific tag
 * @param project The project to check
 * @param tag The tag to check for
 * @returns Whether the project has the tag
 */
export const projectHasTag = (project: ProjectType, tag: string): boolean => {
  return project.tags.some(t => normalizedStringCompare(t, tag));
};

/**
 * Filter projects based on criteria
 * @param projects The projects to filter
 * @param category Optional category filter
 * @param location Optional location filter
 * @param tag Optional tag filter
 * @param season Optional season filter
 * @returns Filtered projects
 */
export const filterProjects = (
  projects: ProjectType[],
  category?: string | null,
  location?: string | null,
  tag?: string | null,
  season?: string | null
): ProjectType[] => {
  // If no filters are applied, return all projects
  if (!category && !location && !tag && !season) {
    return [...projects];
  }

  // Apply filters in a hierarchical way
  let filtered = [...projects];

  // Step 1: Filter by season (if specified)
  if (season) {
    filtered = filtered.filter(project => projectMatchesSeason(project, season as SeasonType));
  }

  // Step 2: Filter by category (if specified)
  if (category) {
    filtered = filtered.filter(project => projectMatchesCategory(project, category));
  }

  // Step 3: Filter by location (if specified)
  if (location) {
    filtered = filtered.filter(project => projectMatchesLocation(project, location));
  }

  // Step 4: Filter by tag (if specified)
  if (tag) {
    filtered = filtered.filter(project => projectHasTag(project, tag));
  }

  return filtered;
};

/**
 * Check if a project matches a service
 * @param project The project to check
 * @param service The service to check against
 * @returns Whether the project matches the service
 */
export const projectMatchesService = (project: ProjectType, service: ServiceType): boolean => {
  // First, try to find a category that maps to this service
  const matchingCategory = Object.values(SERVICE_CATEGORIES).find(
    category => category.serviceId === service.id
  );

  // If we found a matching category, check if the project category matches it
  if (matchingCategory) {
    return normalizedStringCompare(project.category, matchingCategory.name);
  }

  // Fall back to string matching for backward compatibility
  const normalizedCategory = normalizeString(project.category);
  const normalizedServiceTitle = normalizeString(service.title);

  // Check if one contains the other (handles singular/plural variations)
  return normalizedCategory.includes(normalizedServiceTitle) ||
         normalizedServiceTitle.includes(normalizedCategory);
};

/**
 * Get projects that match a specific service
 * @param projects The projects to filter
 * @param serviceId The ID of the service to match against
 * @param services The list of services to find the service by ID
 * @param location Optional location filter
 * @returns Filtered projects that match the service
 */
export const getProjectsForService = (
  projects: ProjectType[],
  serviceId: string,
  services: ServiceType[],
  location?: string | null
): ProjectType[] => {
  // Find the service with the given ID
  const service = services.find(s => s.id === serviceId);

  if (!service) {
    // If service not found, fall back to direct category matching
    return projects.filter(project =>
      normalizedStringCompare(project.category, serviceId) &&
      (!location || projectMatchesLocation(project, location))
    );
  }

  // Filter projects by service title match
  let filtered = projects.filter(project => projectMatchesService(project, service));

  // Apply location filter if needed
  if (location) {
    filtered = filtered.filter(project => projectMatchesLocation(project, location));
  }

  return filtered;
};

/**
 * Get services that have matching projects
 * @param services The services to filter
 * @param projects The projects to check against
 * @param location Optional location filter to apply to projects
 * @returns Services that have at least one matching project
 */
export const getServicesWithProjects = (
  services: ServiceType[],
  projects: ProjectType[],
  location?: string | null
): ServiceType[] => {
  // If location is specified, filter projects by location first
  let filteredProjects = [...projects];
  if (location) {
    filteredProjects = filteredProjects.filter(project =>
      projectMatchesLocation(project, location)
    );
  }

  // Return services that have at least one matching project
  return services.filter(service =>
    filteredProjects.some(project => projectMatchesService(project, service))
  );
};

/**
 * Get unique locations from projects filtered by service
 * @param projects The projects to extract locations from
 * @param serviceId Optional service ID filter
 * @param services The list of services to find the service by ID
 * @returns Array of unique locations
 */
export const getUniqueProjectLocations = (
  projects: ProjectType[],
  serviceId?: string | null,
  services?: ServiceType[]
): string[] => {
  let filteredProjects = [...projects];

  // If a service ID is specified and services are provided, filter by service
  if (serviceId && services && services.length > 0) {
    filteredProjects = getProjectsForService(projects, serviceId, services);
  }

  // Extract unique locations
  const locationSet = new Set(
    filteredProjects.map(project => project.location)
  );

  return Array.from(locationSet);
};

/**
 * Get unique features from a list of services
 * @param services The services to extract features from
 * @param season Optional season filter (parent)
 * @param category Optional category filter (child)
 * @returns Array of unique features
 */
export const getUniqueFeatures = (
  services: ServiceType[],
  season?: string | null,
  category?: string | null
): string[] => {
  // If a season is selected but no category, show features from the season mapping
  if (season && !category) {
    const seasonMapping = SEASONAL_SERVICES[season as keyof typeof SEASONAL_SERVICES];
    if (!seasonMapping) return [];
    return [...seasonMapping.features];
  }

  // Start with all services
  let filteredServicesList = [...services];

  // Step 1: Filter by season first (top-level parent filter)
  if (season) {
    // Use strict mode for winter season
    const strictMode = season === 'vinter';
    filteredServicesList = filteredServicesList.filter(service =>
      serviceMatchesSeason(service, season as SeasonType, strictMode)
    );
  }

  // Note: We don't filter by category here because features are at a higher level than categories
  // in our parent/child hierarchy. Features are children of seasons but parents of categories.

  // Extract all features from the filtered services
  // This ensures the feature dropdown only shows options that are relevant to the current selection
  const featuresSet = new Set(
    filteredServicesList.flatMap(service => service.features || [])
  );

  return Array.from(featuresSet);
};
