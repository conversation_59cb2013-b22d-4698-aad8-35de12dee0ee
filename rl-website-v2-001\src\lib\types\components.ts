import { ReactNode } from 'react';
import { ProjectType } from './content';

/**
 * Type definitions for UI components
 * 
 * This file contains type definitions for props used by UI components
 * throughout the application.
 */

// Button component props
export interface ButtonProps {
  children: ReactNode;
  to?: string;
  onClick?: () => void;
  type?: "button" | "submit" | "reset";
  variant?: "primary" | "secondary" | "outline";
  className?: string;
  fullWidth?: boolean;
}

// Container component props
export interface ContainerProps {
  children: ReactNode;
  className?: string;
  maxWidth?: "xs" | "sm" | "md" | "lg" | "xl" | "2xl" | "full";
  as?: React.ElementType;
}

// Hero component props
export interface HeroProps {
  title?: string;
  subtitle?: string;
  backgroundImage?: string;
  location?: string;
  yearEstablished?: string;
  actionLink?: string;
  actionText?: string;
  height?: "small" | "medium" | "large" | "full";
  overlay?: "none" | "light" | "dark" | "gradient";
  textAlignment?: "left" | "center" | "right";
  textColor?: "light" | "dark";
}

// Card component props
export interface CardProps {
  title: string;
  description?: string;
  image?: string;
  link?: string;
  variant?: "default" | "hover" | "outline";
  className?: string;
}

// PageSection component props
export interface PageSectionProps {
  children: ReactNode;
  className?: string;
  width?: "full" | "wide" | "medium" | "narrow";
  background?: "white" | "light" | "dark" | "primary";
  spacing?: "none" | "small" | "medium" | "large";
  centered?: boolean;
  as?: React.ElementType;
  id?: string;
}

// SectionHeading component props
export interface SectionHeadingProps {
  title: string;
  subtitle?: string;
  highlightedWord?: string;
  align?: 'left' | 'center' | 'right';
  size?: 'small' | 'medium' | 'large';
  className?: string;
  subtitleClassName?: string;
}

// ContentGrid component props
export interface ContentGridProps {
  children: ReactNode;
  columns?: 1 | 2 | 3 | 4;
  mdColumns?: 1 | 2 | 3 | 4;
  lgColumns?: 1 | 2 | 3 | 4;
  gap?: 'small' | 'medium' | 'large';
  className?: string;
}

// Project-related component props
export interface ProjectCardProps {
  project: ProjectType;
  variant?: "default" | "featured" | "grid";
  className?: string;
  showTestimonial?: boolean;
}

export interface ProjectGridProps {
  projects: ProjectType[];
  selectedCategory?: string;
  className?: string;
}

export interface ProjectFilterProps {
  categories: string[];
  locations: string[];
  tags: string[];
  onFilterChange: (type: string, value: string) => void;
  selectedFilters: {
    category: string | null;
    location: string | null;
    tag: string | null;
  };
}

// Gallery-related component props
export interface GalleryImage {
  src: string;
  alt: string;
  width: number;
  height: number;
}

export interface GalleryProps {
  images: GalleryImage[];
  className?: string;
}

// Form component props
export interface InputProps {
  name: string;
  label?: string;
  type?: string;
  placeholder?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  error?: string;
  required?: boolean;
  className?: string;
}

export interface TextareaProps {
  name: string;
  label?: string;
  placeholder?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
  error?: string;
  required?: boolean;
  rows?: number;
  className?: string;
}

export interface SelectProps {
  name: string;
  label?: string;
  options: { value: string; label: string }[];
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLSelectElement>) => void;
  error?: string;
  required?: boolean;
  className?: string;
}