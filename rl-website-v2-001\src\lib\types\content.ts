/**
 * Type definitions for content data models
 *
 * This file contains type definitions for content data models
 * used throughout the application.
 */

// Season-related types
export type SeasonType = 'vår' | 'sommer' | 'høst' | 'vinter';
export type EnglishSeasonType = 'spring' | 'summer' | 'fall' | 'winter';

// Project-related types
export interface ProjectType {
  id: string;
  title: string;
  description: string;
  location: string;
  completionDate?: string;
  date?: string;
  image: string;
  images?: string[];
  category: string;
  tags: string[];
  featured?: boolean;
  specifications?: {
    size: string;
    duration: string;
    materials: string[];
    features: string[];
  };
  testimonial?: {
    quote: string;
    author: string;
  };
}

// Legacy Project interface for backward compatibility
export interface Project {
  id: string;
  title: string;
  description: string;
  category: string;
  location: string;
  tags: string[];
  images: string[];
  date: string;
  featured?: boolean;
}

// Service-related types
export interface ServiceType {
  id: string;
  title: string;
  description: string;
  shortDescription?: string;
  image: string;
  icon?: string;
  longDescription?: string;
  features: string[];
  benefits?: string[];
  seasonal?: boolean;
  imageCategory?: string;
}

// Legacy Service interface for backward compatibility
export interface Service {
  id: string;
  title: string;
  description: string;
  shortDescription: string;
  icon?: string;
  image: string;
  features: string[];
  benefits?: string[];
  seasonal?: boolean;
  pricing?: {
    basePrice: number;
    unit: string;
    note?: string;
  };
  seo?: {
    title: string;
    description: string;
    keywords: string[];
  };
}

export interface ServiceArea {
  city: string;
  distance: string;
  description?: string;
  isBase?: boolean;
  coordinates?: Coordinates;
}

// Testimonial-related types
export interface TestimonialType {
  id?: string;
  name: string;
  location: string;
  text: string;
  rating: number;
  projectType: string;
  image?: string;
  date?: string;
  // New fields for source information
  source?: string;        // Source name (e.g., "MittAnbud", "Google", etc.)
  sourceUrl?: string;     // URL to the original review
  sourceIcon?: string;    // Optional icon for the source
}

// Legacy Testimonial interface for backward compatibility
export interface Testimonial {
  id: string;
  name?: string;
  location?: string;
  text: string;
  rating: number;
  projectType: string;
  image?: string;
  date?: string;
  author?: {
    name: string;
    location: string;
  };
  verified?: boolean;
}

// Team-related types
export interface TeamMember {
  id: string;
  name: string;
  title: string;
  contact?: {
    phone: string;
    email: string;
  };
  phone?: string;
  email?: string;
  image: string;
  expertise?: string[];
  certifications?: string[];
}

// Location-related types
export interface Coordinates {
  lat: number;
  lng: number;
}

export interface Region {
  name: string;
  description: string;
  features: string[];
}

export interface Address {
  street: string;
  postalCode: string;
  city: string;
  municipality: string;
  county: string;
  country: string;
}

export interface Location {
  id: string;
  name: string;
  type?: string;
  coordinates?: Coordinates;
  distance?: string;
  description: string;
  image?: string;
  coverage?: {
    radius: number;
    unit: string;
  };
  features?: string[];
}