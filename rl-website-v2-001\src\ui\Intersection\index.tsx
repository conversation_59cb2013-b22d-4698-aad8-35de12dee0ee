import React, { useEffect, useRef, useState } from 'react';
import { cn  } from '@/lib/utils';

interface IntersectionProps {
  children: React.ReactNode;
  className?: string;
  threshold?: number;
  rootMargin?: string;
  onIntersect?: () => void;
  once?: boolean;
  as?: keyof JSX.IntrinsicElements;
}

const Intersection: React.FC<IntersectionProps> = ({
  children,
  className,
  threshold = 0.1,
  rootMargin = '0px',
  onIntersect,
  once = true,
  // as prop is not used but kept for API compatibility
  // @ts-ignore
  as = 'div'
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          onIntersect?.();
          if (once && ref.current) {
            observer.unobserve(ref.current);
          }
        } else if (!once) {
          setIsVisible(false);
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [threshold, rootMargin, onIntersect, once]);

  return (
    <div
      ref={ref}
      className={cn(
        'transition-opacity duration-700',
        isVisible ? 'opacity-100' : 'opacity-0',
        className
      )}
    >
      {children}
    </div>
  );
};

export { Intersection };
export default Intersection;