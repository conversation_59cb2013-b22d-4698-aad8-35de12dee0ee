/**
 * Seasonal Constants
 *
 * This file contains all constants related to seasonal data and functionality.
 * It serves as a single source of truth for seasonal information throughout the application.
 */

import { SeasonType } from '@/lib/types/content';

/**
 * Norwegian season names
 */
export const SEASONS: SeasonType[] = ['vår', 'sommer', 'høst', 'vinter'];

/**
 * Mapping of Norwegian season names to English
 */
export const SEASON_MAPPING = {
  'vår': 'spring',
  'sommer': 'summer',
  'høst': 'fall',
  'vinter': 'winter'
} as const;

/**
 * Mapping of seasons to display names
 */
export const SEASON_DISPLAY_NAMES = {
  'vår': 'Våren',
  'sommer': 'Sommeren',
  'høst': 'Høsten',
  'vinter': 'Vinteren'
} as const;

/**
 * Seasonal services mapping
 * Maps seasons to relevant service categories and features
 */
export const SEASONAL_SERVICES = {
  'vår': {
    categories: ['Hekk', 'Ferdigplen', 'Beplantning', 'Platting', '<PERSON><PERSON><PERSON><PERSON><PERSON>'],
    features: ['Planting', 'Vanning', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Anlegg', '<PERSON>sse']
  },
  'sommer': {
    categories: ['Platting', 'Cortenstål', 'Belegningsstein'],
    features: ['Vedlikehold', 'Vanning', 'Beplantning']
  },
  'høst': {
    categories: ['Støttemurer', 'Kantstein', 'Trapper'],
    features: ['Beskjæring', 'Drenering', 'Vinterklargjøring']
  },
  'vinter': {
    categories: ['Planlegging', 'Design', 'Prosjektering'],
    features: ['Planlegging', 'Design', 'Prosjektering']
  }
} as const;

/**
 * Seasonal projects mapping
 * Maps seasons to relevant project categories and tags
 */
export const SEASONAL_PROJECTS = {
  'vår': {
    categories: ['Hekk og Beplantning', 'Ferdigplen', 'Platting', 'Belegningsstein'],
    tags: ['beplantning', 'plen', 'hage', 'terrasse', 'innkjørsel']
  },
  'sommer': {
    categories: ['Platting', 'Cortenstål', 'Belegningsstein'],
    tags: ['terrasse', 'uteplass', 'innkjørsel']
  },
  'høst': {
    categories: ['Støttemur', 'Kantstein', 'Trapper og Repoer'],
    tags: ['terrengforming', 'støttemur', 'trapp']
  },
  'vinter': {
    categories: ['Cortenstål', 'Støttemur', 'Belegningsstein'],
    tags: ['planlegging', 'design']
  }
} as const;

/**
 * Month to season mapping
 * Maps month indices (0-11) to seasons
 */
export const MONTH_TO_SEASON: Record<number, SeasonType> = {
  0: 'vinter', // January
  1: 'vinter', // February
  2: 'vår',    // March
  3: 'vår',    // April
  4: 'vår',    // May
  5: 'sommer', // June
  6: 'sommer', // July
  7: 'sommer', // August
  8: 'høst',   // September
  9: 'høst',   // October
  10: 'høst',  // November
  11: 'vinter' // December
};
