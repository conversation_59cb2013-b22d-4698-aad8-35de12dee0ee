import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
    // Load env file based on `mode` in the env directory
    const env = loadEnv(mode, path.resolve(__dirname, "config/env"), "");

    const isProduction = mode === "production";
    const isStaging = mode === "staging";
    const isDevelopment = mode === "development";

    return {
        plugins: [react()],

        // Base public path when served in development or production
        base: "/",

        // Directory to serve as plain static assets
        publicDir: "public",

        // Resolve configuration
        resolve: {
            alias: {
                "@": path.resolve(__dirname, "src"),
            },
        },

        // Build configuration
        build: {
            // Output directory (within website directory)
            outDir: "./dist",

            // Empty output directory before build
            emptyOutDir: true,

            // Generate sourcemaps for production build
            sourcemap: !isProduction,

            // Minify option
            minify: isProduction ? "esbuild" : false,

            // Target browsers
            target: "es2015",

            // Rollup options
            rollupOptions: {
                output: {
                    // Chunk files
                    manualChunks: {
                        vendor: ["react", "react-dom", "react-router-dom"],
                        ui: ["framer-motion", "lucide-react"],
                    },
                },
            },
        },

        // Server options
        server: {
            port: 5173,
            strictPort: false,
            open: true,
            cors: true,
            host: true,
        },

        // Preview options (for previewing production builds)
        preview: {
            port: 4173,
            strictPort: false,
            open: true,
            cors: true,
        },

        // Dependency optimization
        optimizeDeps: {
            exclude: ["lucide-react"],
        },

        // Define global constants
        define: {
            __APP_ENV__: JSON.stringify(mode),
            __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
            __BUILD_DATE__: JSON.stringify(new Date().toISOString()),
        },
    };
});
