/**
 * Location Constants
 * 
 * This file contains all constants related to location data and functionality.
 * It serves as a single source of truth for location information throughout the application.
 */

/**
 * Primary service area information
 */
export const PRIMARY_AREA = {
  name: 'Ringerike',
  displayName: 'Ringerike',
  adjectiveForm: 'Ringerikes',
  regionName: 'ringeriksregionen',
  seoKeywords: ['anleggsgartner ringerike', 'hagearbeid ringerike', 'belegningsstein ringerike'],
  description: 'Fra Tyrifjordens bredder til åsene rundt Hønefoss',
  seoDescription: 'Anleggsgartner og maskinentreprenør med lang erfaring i ringeriksregionen. Vi skaper varige uterom tilpasset dine ønsker og lokale forhold.'
};

/**
 * Company base location information
 */
export const COMPANY_BASE = {
  name: 'Røyse',
  municipality: 'Hole kommune',
  county: 'Viken',
  fullAddress: 'Røyse, Hole kommune',
  coordinates: {
    lat: 60.0584,
    lng: 10.2551
  },
  phone: '+47 123 45 678',
  email: '<EMAIL>'
};

/**
 * Service areas with detailed information
 */
export const SERVICE_AREAS = {
  ringerike: {
    name: 'Ringerike',
    slug: 'ringerike',
    distance: '0-10 km',
    isCore: true,
    coordinates: {
      lat: 60.1694,
      lng: 10.2551
    },
    terrainDescription: 'Fra Tyrifjordens bredder til åsene rundt Hønefoss',
    seoDescription: 'Spesialister på lokale grunnforhold og terreng.',
    popularServices: ['Belegningsstein', 'Støttemur', 'Hagedesign']
  },
  hole: {
    name: 'Hole',
    slug: 'hole',
    distance: '0-5 km',
    isCore: true,
    coordinates: {
      lat: 60.0584,
      lng: 10.3051
    },
    terrainDescription: 'Med utsikt over Tyrifjorden og variert terreng',
    seoDescription: 'Lokalkjente anleggsgartnere med fokus på kvalitet og holdbarhet.',
    popularServices: ['Ferdigplen', 'Beplantning', 'Platting']
  },
  honefoss: {
    name: 'Hønefoss',
    slug: 'honefoss',
    distance: '5-15 km',
    isCore: true,
    coordinates: {
      lat: 60.1694,
      lng: 10.2551
    },
    terrainDescription: 'Byområde med varierte behov for uterom',
    seoDescription: 'Profesjonelle anleggsgartnertjenester for både private og bedrifter.',
    popularServices: ['Belegningsstein', 'Støttemur', 'Platting']
  },
  jevnaker: {
    name: 'Jevnaker',
    slug: 'jevnaker',
    distance: '15-25 km',
    isCore: false,
    coordinates: {
      lat: 60.2384,
      lng: 10.3907
    },
    terrainDescription: 'Variert terreng med både flate områder og skråninger',
    seoDescription: 'Erfarne anleggsgartnere som kjenner lokale forhold.',
    popularServices: ['Støttemur', 'Belegningsstein', 'Trapper']
  }
};

/**
 * Terrain features in the service area
 */
export const TERRAIN_FEATURES = [
  'lokale grunnforhold',
  'Tyrifjordens bredder',
  'åsene rundt Hønefoss',
  'variert terreng',
  'skrånende tomter',
  'leirholdig jord'
];

/**
 * Location-specific content helpers
 */

/**
 * Get the hero subtitle for a specific location
 */
export const getLocationHeroSubtitle = (locationKey?: keyof typeof SERVICE_AREAS) => {
  if (locationKey && SERVICE_AREAS[locationKey]) {
    const location = SERVICE_AREAS[locationKey];
    return `Som anleggsgartner og maskinentreprenør med lang erfaring i ${PRIMARY_AREA.regionName}, med base på ${COMPANY_BASE.name} skaper vi varige uterom tilpasset dine ønsker i ${location.name}.`;
  }
  
  // Default subtitle for the primary area
  return `Som anleggsgartner og maskinentreprenør med lang erfaring i ${PRIMARY_AREA.regionName}, med base på ${COMPANY_BASE.name} skaper vi varige uterom tilpasset dine ønsker. Vi kjenner grunnforhold i regionen og fokuserer på høy kvalitet i både service og utførelse.`;
};

/**
 * Get the services heading for a specific location
 */
export const getServicesHeading = (locationKey?: keyof typeof SERVICE_AREAS) => {
  if (locationKey && SERVICE_AREAS[locationKey]) {
    return `Tjenester for ${SERVICE_AREAS[locationKey].name}`;
  }
  
  // Default heading for the primary area
  return `Tjenester for ${PRIMARY_AREA.adjectiveForm} terreng`;
};

/**
 * Get the services subheading for a specific location
 */
export const getServicesSubheading = (locationKey?: keyof typeof SERVICE_AREAS) => {
  if (locationKey && SERVICE_AREAS[locationKey]) {
    const location = SERVICE_AREAS[locationKey];
    return `${location.terrainDescription} – vi leverer løsninger skreddersydd for lokale forhold og klima i ${location.name}.`;
  }
  
  // Default subheading for the primary area
  return `${SERVICE_AREAS.ringerike.terrainDescription} – vi leverer løsninger skreddersydd for lokale forhold og klima.`;
};

/**
 * Get SEO metadata for a specific location
 */
export const getLocationSeoMetadata = (locationKey?: keyof typeof SERVICE_AREAS) => {
  if (locationKey && SERVICE_AREAS[locationKey]) {
    const location = SERVICE_AREAS[locationKey];
    
    return {
      title: `Anleggsgartner i ${location.name} | Ringerike Landskap`,
      description: `Profesjonelle anleggsgartnertjenester i ${location.name}. ${location.seoDescription}`,
      keywords: [
        `anleggsgartner ${location.name.toLowerCase()}`,
        `hagearbeid ${location.name.toLowerCase()}`,
        `belegningsstein ${location.name.toLowerCase()}`,
        ...PRIMARY_AREA.seoKeywords
      ],
      schema: {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": "Ringerike Landskap",
        "image": "/images/hero/hero-home-main.webp",
        "address": {
          "@type": "PostalAddress",
          "addressLocality": COMPANY_BASE.municipality,
          "addressRegion": COMPANY_BASE.county,
          "addressCountry": "Norway"
        },
        "geo": {
          "@type": "GeoCoordinates",
          "latitude": location.coordinates.lat,
          "longitude": location.coordinates.lng
        },
        "url": `https://ringerikelandskap.no/tjenester/${location.slug}`,
        "telephone": COMPANY_BASE.phone,
        "priceRange": "$$",
        "areaServed": location.name
      }
    };
  }
  
  // Default SEO metadata for the primary area
  return {
    title: "Anleggsgartner i Ringerike | Ringerike Landskap",
    description: PRIMARY_AREA.seoDescription,
    keywords: PRIMARY_AREA.seoKeywords,
    schema: {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": "Ringerike Landskap",
      "image": "/images/hero/hero-home-main.webp",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": COMPANY_BASE.municipality,
        "addressRegion": COMPANY_BASE.county,
        "addressCountry": "Norway"
      },
      "url": "https://ringerikelandskap.no",
      "telephone": COMPANY_BASE.phone,
      "priceRange": "$$",
      "areaServed": PRIMARY_AREA.name
    }
  };
};
