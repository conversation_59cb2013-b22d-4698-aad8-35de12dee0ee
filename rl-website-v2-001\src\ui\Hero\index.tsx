import React from "react";
import { Link } from "react-router-dom";
import { ChevronDown } from "lucide-react";
import { encodeImagePath  } from '@/lib/utils/paths';

export interface HeroProps {
    title: string;
    subtitle?: string;
    backgroundImage: string;
    location?: string;
    actionLink?: string;
    actionText?: string;
    height?: "small" | "medium" | "large" | "full";
    overlay?: "none" | "light" | "dark" | "gradient";
    textAlignment?: "left" | "center" | "right";
    textColor?: "light" | "dark";
    yearEstablished?: string;
}

const Hero: React.FC<HeroProps> = ({
    title,
    subtitle,
    backgroundImage = "/images/hero/hero-home-main.webp",
    location = "Røyse, Hole kommune",
    actionLink,
    actionText = "Se våre prosjekter",
    height = "large",
    overlay = "gradient",
    textAlignment = "center",
    textColor = "light",
    yearEstablished = "2015",
}) => {
    const heightClasses = {
        small: "h-[300px]",
        medium: "h-[500px]",
        large: "h-[calc(100vh-64px)] min-h-[600px] max-h-[800px]",
        full: "h-screen",
    };

    // These overlay classes are now additional effects on top of the main gradients
    const overlayClasses = {
        none: "",
        light: "after:absolute after:inset-0 after:bg-white/30 after:z-[1]",
        dark: "after:absolute after:inset-0 after:bg-black/50 after:z-[1]",
        gradient:
            "after:absolute after:inset-0 after:bg-gradient-to-t after:from-black/70 after:to-transparent after:z-[1]",
    };

    const textAlignmentClasses = {
        left: "text-left items-start",
        center: "text-center items-center",
        right: "text-right items-end",
    };

    const textColorClasses = {
        light: "text-white",
        dark: "text-gray-900",
    };

    const scrollToContent = () => {
        window.scrollTo({
            top: window.innerHeight,
            behavior: "smooth",
        });
    };

    return (
        <section
            className={`relative overflow-hidden ${heightClasses[height]} ${overlayClasses[overlay]}`}
        >
            {/* Background image */}
            <div
                className="absolute inset-0 transform scale-105"
                style={{
                    backgroundImage: `url(${encodeImagePath(backgroundImage)})`,
                    backgroundSize: "cover",
                    backgroundPosition: "center",
                    backgroundAttachment: "fixed",
                }}
                role="img"
                aria-label="Landskapsprosjekt i Ringerike-området"
            />

            {/* Base overlay - Always applied to ALL pages */}
            <div
                className="absolute inset-0 bg-black/20"
                aria-hidden="true"
            ></div>

            {/* Center square gradient overlay - Always applied to ALL pages */}
            <div
                className="absolute inset-0"
                style={{
                    background: `
                    linear-gradient(
                      50deg,
                      transparent 15%,
                      rgba(0,0,0,0.75) 32%,
                      rgba(0,0,0,0.6) 72%,
                      transparent 85%
                    )
                  `,
                    pointerEvents: "none",
                }}
                aria-hidden="true"
            />

            {/* Subtle corner darkening - Always applied to ALL pages */}
            <div
                className="absolute inset-0"
                style={{
                    background:
                        "radial-gradient(circle at center, transparent 78%, rgba(0,0,0,0.85) 100%)",
                    pointerEvents: "none",
                }}
                aria-hidden="true"
            />

            {/* Content */}
            <div className="relative z-10 h-full flex flex-col justify-center px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
                <div
                    className={`flex flex-col ${textAlignmentClasses[textAlignment]} gap-4 max-w-3xl mx-auto`}
                >
                    {location && (
                        <div className="inline-flex items-center bg-green-500/90 text-white px-4 py-1.5 rounded-full text-sm font-medium mb-2">
                            {location} • Etablert {yearEstablished}
                        </div>
                    )}

                    <h1
                        className={`text-4xl sm:text-5xl lg:text-6xl font-bold ${textColorClasses[textColor]}`}
                    >
                        {title}
                    </h1>

                    {subtitle && (
                        <p
                            className={`text-lg sm:text-xl mt-2 ${textColorClasses[textColor]} opacity-90 max-w-2xl`}
                        >
                            {subtitle}
                        </p>
                    )}

                    {actionText && actionLink && (
                        <div className="mt-8">
                            <Link
                                to={actionLink}
                                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                            >
                                {actionText}
                            </Link>
                        </div>
                    )}

                    {!actionLink && (
                        <button
                            onClick={scrollToContent}
                            className="mt-8 inline-flex flex-col items-center gap-2 text-white/80 hover:text-white transition-colors"
                        >
                            <span className="text-sm font-medium">
                                Scroll ned for mer
                            </span>
                            <ChevronDown className="w-6 h-6 animate-bounce" />
                        </button>
                    )}
                </div>
            </div>
        </section>
    );
};

export { Hero };
export default Hero;
