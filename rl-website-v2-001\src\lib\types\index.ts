/**
 * Central export file for all type definitions in the application
 *
 * This file exports all types from the lib/types directory,
 * providing a single import point for all type definitions.
 */

// Content data models - primary source of shared types
export * from './content';

// Component props
export * from './components';

// Re-export specific types from locations and contact
export type { CompanyBase as LocationData } from './locations';
export type { PrimaryArea as LocationMetadata } from './locations';
export type { ContactInformation as ContactInfo } from './contact';
export type { SocialMedia as SocialLink } from './contact';

// Common utility types
export interface Dictionary<T> {
  [key: string]: T;
}

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
