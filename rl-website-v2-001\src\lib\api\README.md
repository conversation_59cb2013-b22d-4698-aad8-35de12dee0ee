# API Layer

This directory contains the API layer for the application, which provides a centralized way to access data.

## Structure

- `index.ts`: Main entry point that re-exports from the enhanced API
- `enhanced.ts`: Enhanced API implementation with logging, caching, and error handling
- `sync.ts`: Synchronous versions of API functions (deprecated)

## Usage

### Recommended: Async API with useData Hook

```tsx
import { useData } from '@/lib/hooks';
import { getServices } from '@/lib/api';

const MyComponent = () => {
  const { data: services, loading, error } = useData(getServices, []);
  
  if (loading) return <LoadingIndicator />;
  if (error) return <ErrorMessage error={error} />;
  
  return (
    <div>
      {services?.map(service => (
        <ServiceCard key={service.id} service={service} />
      ))}
    </div>
  );
};
```

### Deprecated: Synchronous API

```tsx
import { getServices } from '@/lib/api/sync';

const MyComponent = () => {
  const services = getServices();
  
  return (
    <div>
      {services.map(service => (
        <ServiceCard key={service.id} service={service} />
      ))}
    </div>
  );
};
```

## API Functions

### Services

- `getServices()`: Get all services
- `getServiceById(id)`: Get a service by ID
- `getFilteredServices(category, feature, season)`: Get services filtered by criteria
- `getSeasonalServices(limit)`: Get seasonal services based on current season

### Projects

- `getProjects()`: Get all projects
- `getProjectById(id)`: Get a project by ID
- `getFilteredProjects(category, location, tag, season)`: Get projects filtered by criteria
- `getSeasonalProjects(limit)`: Get seasonal projects based on current season
- `getProjectCategories()`: Get unique project categories
- `getProjectLocations()`: Get unique project locations
- `getProjectTags()`: Get unique project tags

### Testimonials

- `getTestimonials()`: Get all testimonials
- `getFilteredTestimonials(rating)`: Get testimonials filtered by rating
- `getTestimonialRatingCounts()`: Get testimonial rating counts

### Other

- `getServiceAreas()`: Get service areas
- `getSeasonDisplayName(season)`: Get season display name
- `clearCache(cacheKey)`: Clear the cache

## Migration

We are in the process of consolidating all data access to use this API layer. See the [API Migration Plan](../../docs/API_MIGRATION_PLAN.md) for details.
