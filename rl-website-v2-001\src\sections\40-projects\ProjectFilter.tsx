import React from 'react';
import { Filter } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProjectFilterProps {
  categories: string[];
  locations: string[];
  tags?: string[];
  selectedFilters: {
    category?: string;
    location?: string;
    tag?: string;
  };
  onFilterChange: (type: 'category' | 'location' | 'tag', value: string) => void;
}

export const ProjectFilter: React.FC<ProjectFilterProps> = ({
  categories,
  locations,
  tags = [],
  selectedFilters,
  onFilterChange
}) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow-sm">
      <div className="flex items-center gap-2 mb-4 text-gray-700 font-medium">
        <Filter className="w-5 h-5" />
        <h3>Filtrer prosjekter</h3>
      </div>

      {categories.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium mb-2">Kategori</h4>
          <div className="flex flex-wrap gap-2">
            <button
              className={cn(
                'px-3 py-1 text-sm rounded-full',
                !selectedFilters.category
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              )}
              onClick={() => onFilterChange('category', '')}
            >
              Alle
            </button>
            {categories.map((category) => (
              <button
                key={category}
                className={cn(
                  'px-3 py-1 text-sm rounded-full',
                  selectedFilters.category === category
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                )}
                onClick={() => onFilterChange('category', category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      )}

      {locations.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium mb-2">Sted</h4>
          <div className="flex flex-wrap gap-2">
            <button
              className={cn(
                'px-3 py-1 text-sm rounded-full',
                !selectedFilters.location
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              )}
              onClick={() => onFilterChange('location', '')}
            >
              Alle steder
            </button>
            {locations.map((location) => (
              <button
                key={location}
                className={cn(
                  'px-3 py-1 text-sm rounded-full',
                  selectedFilters.location === location
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                )}
                onClick={() => onFilterChange('location', location)}
              >
                {location}
              </button>
            ))}
          </div>
        </div>
      )}

      {tags.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-2">Stikkord</h4>
          <div className="flex flex-wrap gap-2">
            <button
              className={cn(
                'px-3 py-1 text-sm rounded-full',
                !selectedFilters.tag
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              )}
              onClick={() => onFilterChange('tag', '')}
            >
              Alle
            </button>
            {tags.map((tag) => (
              <button
                key={tag}
                className={cn(
                  'px-3 py-1 text-sm rounded-full',
                  selectedFilters.tag === tag
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                )}
                onClick={() => onFilterChange('tag', tag)}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectFilter;