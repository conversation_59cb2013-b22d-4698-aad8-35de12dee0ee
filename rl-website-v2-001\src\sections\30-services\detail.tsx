
import { useParams, useNavigate } from 'react-router-dom';

import {
  <PERSON>,
  <PERSON><PERSON>,
  PageSection,
  SectionHeading,
  ContentGrid,
  Loading
} from '@/ui';
import { CheckCircle } from 'lucide-react';
import ProjectGallery from '@/sections/40-projects/ProjectGallery';
import { IMAGE_CATEGORIES } from '@/lib/utils/images';
import { useData } from '@/lib/hooks';
import { getServiceById, getServiceImageCategory, getFilteredProjects } from '@/lib/api';

const ServiceDetailPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Use the useData hook to fetch the service and related data
  const { data: service, loading: serviceLoading, error: serviceError } = useData(() => getServiceById(id || ''), [id]);
  const { data: imageCategory } = useData(() => getServiceImageCategory(id || ''), [id]);

  // If the service is not found, redirect to the services page
  if (serviceError || (!serviceLoading && !service)) {
    navigate('/hva');
    return null;
  }

  // Use the useData hook to fetch related projects
  const { data: relatedProjects = [], loading: projectsLoading } =
    useData(() => service ? getFilteredProjects(service.title) : Promise.resolve([]), [service?.title]);

  // Show loading state while data is being fetched
  if (serviceLoading || projectsLoading) {
    return <Loading message="Laster tjenesteinformasjon..." />;
  }

  if (!service) {
      return <Loading message="Laster tjenesteinformasjon..." />;
  }

  return (
      <div>
          <Hero
              title={service.title}
              backgroundImage={service.image}
              subtitle="Se våre gjennomførte prosjekter og la deg inspirere"
          />

          {/* Service description */}
          <PageSection width="medium" spacing="medium">
              <p className="text-lg text-gray-700 leading-relaxed mb-8">
                  {service.longDescription || service.description}
              </p>

              {service.features && (
                  <div className="bg-gray-50 rounded-lg p-6">
                      <h3 className="text-xl font-semibold mb-4">Dette får du:</h3>
                      <ul className="space-y-3">
                          {service.features.map((feature: string, index: number) => (
                              <li key={index} className="flex items-center gap-3">
                                  <CheckCircle className="text-green-500 w-5 h-5 flex-shrink-0" />
                                  <span>{feature}</span>
                              </li>
                          ))}
                      </ul>
                  </div>
              )}
          </PageSection>

          {/* Related Projects */}
          {relatedProjects && relatedProjects.length > 0 && (
              <PageSection width="wide" background="light" spacing="medium">
                  <SectionHeading
                      title={`Våre ${service.title.toLowerCase()}-prosjekter`}
                      subtitle={`Se eksempler på våre gjennomførte ${service.title.toLowerCase()}-prosjekter i Ringerike-området`}
                      align="center"
                      highlightedWord={service.title.toLowerCase()}
                  />

                  <ContentGrid
                      columns={1}
                      mdColumns={2}
                      lgColumns={3}
                      gap="medium"
                  >
                      {relatedProjects.map(project => (
                          <div key={project.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                              <div className="h-48 overflow-hidden">
                                  <img
                                      src={project.image}
                                      alt={project.title}
                                      className="w-full h-full object-cover"
                                  />
                              </div>
                              <div className="p-4">
                                  <h3 className="text-lg font-semibold mb-2">{project.title}</h3>
                                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">{project.description}</p>
                                  <Button
                                      to={`/prosjekter/${project.id}`}
                                      variant="outline"
                                      className="w-full"
                                  >
                                      Se prosjektet
                                  </Button>
                              </div>
                          </div>
                      ))}
                  </ContentGrid>
              </PageSection>
          )}

          {/* Project Gallery */}
          {imageCategory && (
              <PageSection width="wide" spacing="medium">
                  <SectionHeading
                      title="Vårt bildegalleri"
                      subtitle={`Se flere bilder av våre ${service.title.toLowerCase()}-prosjekter`}
                      align="center"
                      highlightedWord="bildegalleri"
                  />

                  <ProjectGallery
                      category={imageCategory as keyof typeof IMAGE_CATEGORIES}
                  />
              </PageSection>
          )}

          {/* Contact CTA */}
          <PageSection width="medium" background="primary" spacing="medium" centered={true}>
              <SectionHeading
                  title={`Interessert i ${service.title.toLowerCase()}?`}
                  subtitle="Ta kontakt for en uforpliktende prat om ditt prosjekt. Vi kommer gjerne på befaring for å se på mulighetene."
                  align="center"
                  size="medium"
              />

              <Button
                  to="/kontakt"
                  variant="primary"
                  className="px-8 py-3"
              >
                  Kontakt oss for gratis befaring
              </Button>
          </PageSection>
      </div>
  );
};

export default ServiceDetailPage;
