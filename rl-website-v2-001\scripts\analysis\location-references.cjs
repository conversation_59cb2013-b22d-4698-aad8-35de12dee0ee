/**
 * Location References Analysis Script
 * 
 * This script scans the codebase for location references and generates a report
 * of all occurrences, helping to identify where consolidation is needed.
 */

const fs = require('fs');
const path = require('path');

// Location keywords to search for
const LOCATION_KEYWORDS = [
  'Ringerike',
  'ringerike',
  'Ringerikes',
  'ringerikes',
  'ringeriksregionen',
  'Røyse',
  'røyse',
  'Hole kommune',
  'hole kommune',
  'Hole',
  'Hønefoss',
  'hønefoss',
  'Tyrifjorden',
  'tyrifjorden'
];

// File extensions to scan
const EXTENSIONS_TO_SCAN = ['.tsx', '.ts', '.jsx', '.js', '.md', '.json', '.html'];

// Directories to exclude
const EXCLUDE_DIRS = ['node_modules', 'dist', '.git', 'scripts'];

// Results storage
const results = {
  totalOccurrences: 0,
  fileOccurrences: {},
  keywordCounts: {}
};

// Initialize keyword counts
LOCATION_KEYWORDS.forEach(keyword => {
  results.keywordCounts[keyword] = 0;
});

/**
 * Scans a file for location references
 */
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fileHasOccurrences = false;
    const fileOccurrences = [];

    LOCATION_KEYWORDS.forEach(keyword => {
      // Use regex to find all occurrences
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = content.match(regex);
      
      if (matches && matches.length > 0) {
        fileHasOccurrences = true;
        results.keywordCounts[keyword] += matches.length;
        results.totalOccurrences += matches.length;
        
        // Find line numbers for each occurrence
        const lines = content.split('\n');
        const occurrences = [];
        
        lines.forEach((line, index) => {
          if (line.match(regex)) {
            occurrences.push({
              line: index + 1,
              content: line.trim()
            });
          }
        });
        
        fileOccurrences.push({
          keyword,
          count: matches.length,
          occurrences
        });
      }
    });
    
    if (fileHasOccurrences) {
      results.fileOccurrences[filePath] = fileOccurrences;
    }
  } catch (error) {
    console.error(`Error scanning file ${filePath}:`, error.message);
  }
}

/**
 * Recursively scans a directory for files to analyze
 */
function scanDirectory(dirPath) {
  try {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      if (entry.isDirectory()) {
        // Skip excluded directories
        if (!EXCLUDE_DIRS.includes(entry.name)) {
          scanDirectory(fullPath);
        }
      } else if (entry.isFile()) {
        // Only scan files with specified extensions
        const ext = path.extname(entry.name).toLowerCase();
        if (EXTENSIONS_TO_SCAN.includes(ext)) {
          scanFile(fullPath);
        }
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error.message);
  }
}

// Start the scan from the src directory
console.log('Starting location references analysis...');
const startTime = Date.now();

// Get the root directory (assuming this script is in scripts/analysis)
const rootDir = path.resolve(__dirname, '../../src');
scanDirectory(rootDir);

// Generate the report
const endTime = Date.now();
const scanDuration = ((endTime - startTime) / 1000).toFixed(2);

console.log(`\nLocation References Analysis Complete (${scanDuration}s)`);
console.log(`Total occurrences found: ${results.totalOccurrences}`);
console.log('\nKeyword Counts:');
Object.entries(results.keywordCounts)
  .sort((a, b) => b[1] - a[1])
  .forEach(([keyword, count]) => {
    if (count > 0) {
      console.log(`  ${keyword}: ${count}`);
    }
  });

console.log('\nFiles with Location References:');
Object.keys(results.fileOccurrences).forEach(filePath => {
  const relPath = path.relative(rootDir, filePath);
  const totalInFile = results.fileOccurrences[filePath].reduce((sum, item) => sum + item.count, 0);
  console.log(`  ${relPath} (${totalInFile} occurrences)`);
});

// Write detailed results to a JSON file
const outputPath = path.join(__dirname, 'location-references-report.json');
fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
console.log(`\nDetailed report written to: ${outputPath}`);

// Generate a Markdown report for easier reading
const markdownPath = path.join(__dirname, 'location-references-report.md');
let markdown = '# Location References Analysis Report\n\n';
markdown += `Analysis completed in ${scanDuration} seconds\n\n`;
markdown += `## Summary\n\n`;
markdown += `- Total occurrences: ${results.totalOccurrences}\n\n`;

markdown += `## Keyword Counts\n\n`;
markdown += `| Keyword | Count |\n`;
markdown += `|---------|-------|\n`;
Object.entries(results.keywordCounts)
  .sort((a, b) => b[1] - a[1])
  .forEach(([keyword, count]) => {
    if (count > 0) {
      markdown += `| ${keyword} | ${count} |\n`;
    }
  });

markdown += `\n## Files with Location References\n\n`;
Object.keys(results.fileOccurrences).forEach(filePath => {
  const relPath = path.relative(rootDir, filePath);
  markdown += `\n### ${relPath}\n\n`;
  
  results.fileOccurrences[filePath].forEach(item => {
    markdown += `#### "${item.keyword}" (${item.count} occurrences)\n\n`;
    markdown += `| Line | Content |\n`;
    markdown += `|------|--------|\n`;
    
    item.occurrences.forEach(occurrence => {
      // Escape pipe characters in the content
      const escapedContent = occurrence.content.replace(/\|/g, '\\|');
      markdown += `| ${occurrence.line} | \`${escapedContent}\` |\n`;
    });
    
    markdown += '\n';
  });
});

fs.writeFileSync(markdownPath, markdown);
console.log(`Markdown report written to: ${markdownPath}`);
