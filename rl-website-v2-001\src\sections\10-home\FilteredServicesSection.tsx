
import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar } from 'lucide-react';
import { useData, useSeasonalData } from '@/lib/hooks';
import { getSeasonalServices, getServices } from '@/lib/api';
import { logSeasonalAccess } from '@/lib/utils/debug';
import {
  getServicesHeading,
  getServicesSubheading
} from '@/lib/constants/locations';

const FilteredServicesSection = () => {
  // Use our new seasonal hook to get seasonal data
  const { currentSeason, currentSeasonDisplay } = useSeasonalData();

  // Log seasonal access for debugging
  React.useEffect(() => {
    logSeasonalAccess('FilteredServicesSection', currentSeason, {
      component: 'FilteredServicesSection'
    });
  }, [currentSeason]);

  // Get services from all seasons, not just the current one
  const { data: allServices, loading: allServicesLoading } = useData(() => getServices(), []);
  const { data: seasonalServices, loading: seasonalLoading } = useData(() => getSeasonalServices(4), []);

  // Combine loading states
  const loading = allServicesLoading || seasonalLoading;

  // Combine services, prioritizing seasonal ones but ensuring we have at least 6 services
  const displayServices = React.useMemo(() => {
    if (!seasonalServices || !allServices) return [];

    // Start with seasonal services
    const result = [...seasonalServices];

    // Add other services until we have at least 6 total
    if (result.length < 6) {
      const otherServices = allServices
        .filter(service => !result.some(s => s.id === service.id))
        .slice(0, 6 - result.length);

      result.push(...otherServices);
    }

    return result;
  }, [seasonalServices, allServices]);

  if (loading) {
    return (
      <div>
        <h2 className="text-lg sm:text-xl font-semibold mb-2">
          {getServicesHeading()}
        </h2>
        <p className="text-xs sm:text-sm text-gray-600 mb-3">
          {getServicesSubheading()}
        </p>
        <div className="mb-4 p-2 bg-gray-100 border-l-4 border-gray-200 rounded-r-md animate-pulse"></div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(9)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-200 animate-pulse rounded-lg"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-lg sm:text-xl font-semibold mb-2">
        {getServicesHeading()}
      </h2>
      <p className="text-xs sm:text-sm text-gray-600 mb-3">
        {getServicesSubheading()}
      </p>

      {/* Seasonal tip */}
      <div className="mb-4 p-2 bg-green-50 border-l-4 border-green-500 rounded-r-md">
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-green-600" />
          <p className="text-xs text-gray-700">
            <span className="font-medium">Tips:</span> {currentSeasonDisplay.toLowerCase()} er en god tid for tjenester som ferdigplen, hekk og beplantning.{' '}
            <Link
              to={`/hva?sesong=${currentSeason}`}
              className="text-green-600 hover:underline font-medium"
            >
              Vis tjenester for {currentSeasonDisplay.toLowerCase()}
            </Link>
          </p>
        </div>
      </div>

      {/* Services grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {displayServices.map((service) => (
          <div key={service.id} className="flex items-start gap-3 bg-white p-3 rounded-lg shadow-sm hover:shadow-md transition-shadow">
            <div className="w-20 h-20 flex-shrink-0 rounded-lg overflow-hidden">
              <img
                src={service.image}
                alt={service.title}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="font-medium mb-1 text-sm">{service.title}</h3>
              <p className="text-xs text-gray-600 mb-1 line-clamp-2">{service.description}</p>
              <Link
                to={`/tjenester/${service.id}`}
                className="text-green-600 hover:text-green-700 text-xs font-medium"
              >
                Les mer
              </Link>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 text-center">
        <Link
          to="/hva"
          className="inline-flex items-center text-green-600 hover:text-green-700 font-medium text-sm"
        >
          Se alle våre tjenester
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-3 w-3 ml-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </Link>
      </div>
    </div>
  );
};

export default FilteredServicesSection;
